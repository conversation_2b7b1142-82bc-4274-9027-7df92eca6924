import { Stack } from 'expo-router';
import React from 'react';
import { Colors } from '../../../constants/theme';

export default function TabunganLayout() {
  const headerOptions = {
    headerShown: true,
    headerStyle: {
      backgroundColor: Colors.base,
    },
    headerTintColor: '#fff',
    headerTitleStyle: {
      fontSize: 20,
      fontWeight: 'bold' as const,
    },
    headerShadowVisible: false,
  };

  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: 'Menu Tabungan',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="tambah-setoran"
        options={{
          title: 'Tambah Setoran',
          ...headerOptions,
        }}
      />
      <Stack.Screen
        name="riwayat-setoran"
        options={{
          title: 'Riwayat Setoran',
          ...headerOptions,
        }}
      />
      <Stack.Screen
        name="simulasi-target"
        options={{
          title: 'Simulasi Target',
          ...headerOptions,
        }}
      />
    </Stack>
  );
}
