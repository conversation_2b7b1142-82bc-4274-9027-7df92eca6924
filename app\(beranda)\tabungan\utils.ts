/**
 * Utility functions for the Tabungan (Savings) feature
 * Optimized for elderly users with clear formatting and simple operations
 */

/**
 * Format a number as Indonesian currency (IDR)
 * @param amount - The numeric amount to format
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
  }).format(amount);
};

/**
 * Format a number with thousand separators (for input display)
 * @param value - The string value to format
 * @returns Formatted number string with separators
 */
export const formatNumberWithSeparators = (value: string): string => {
  // Remove non-numeric characters
  const numericValue = value.replace(/[^0-9]/g, '');
  
  // Format with thousand separators
  if (numericValue) {
    return new Intl.NumberFormat('id-ID').format(parseInt(numericValue));
  }
  return '';
};

/**
 * Extract numeric value from formatted string
 * @param formattedValue - The formatted string value
 * @returns Numeric value
 */
export const getNumericValue = (formattedValue: string): number => {
  return parseInt(formattedValue.replace(/[^0-9]/g, '')) || 0;
};

/**
 * Format date for display in Indonesian format
 * @param dateString - ISO date string
 * @returns Formatted date string
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('id-ID', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

/**
 * Format date for display in short format
 * @param dateString - ISO date string
 * @returns Short formatted date string
 */
export const formatShortDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('id-ID', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
};

/**
 * Validate if a string represents a valid positive number
 * @param value - The string value to validate
 * @returns True if valid positive number
 */
export const isValidPositiveNumber = (value: string): boolean => {
  const numericValue = getNumericValue(value);
  return numericValue > 0;
};

/**
 * Calculate savings progress percentage
 * @param current - Current savings amount
 * @param target - Target savings amount
 * @returns Progress percentage (0-100)
 */
export const calculateProgress = (current: number, target: number): number => {
  if (target <= 0) return 0;
  return Math.min((current / target) * 100, 100);
};

/**
 * Calculate months needed to reach target
 * @param current - Current savings amount
 * @param target - Target savings amount
 * @param monthlyDeposit - Monthly deposit amount
 * @returns Object with calculation results
 */
export const calculateTimeToTarget = (
  current: number,
  target: number,
  monthlyDeposit: number
): {
  remaining: number;
  months: number;
  years: number;
  remainingMonths: number;
  isTargetReached: boolean;
} => {
  if (target <= current) {
    return {
      remaining: 0,
      months: 0,
      years: 0,
      remainingMonths: 0,
      isTargetReached: true,
    };
  }

  if (monthlyDeposit <= 0) {
    return {
      remaining: target - current,
      months: 0,
      years: 0,
      remainingMonths: 0,
      isTargetReached: false,
    };
  }

  const remaining = target - current;
  const months = Math.ceil(remaining / monthlyDeposit);
  const years = Math.floor(months / 12);
  const remainingMonths = months % 12;

  return {
    remaining,
    months,
    years,
    remainingMonths,
    isTargetReached: false,
  };
};

/**
 * Get today's date in YYYY-MM-DD format
 * @returns Today's date string
 */
export const getTodayDateString = (): string => {
  return new Date().toISOString().split('T')[0];
};

/**
 * Validate date string format (YYYY-MM-DD)
 * @param dateString - Date string to validate
 * @returns True if valid date format
 */
export const isValidDateString = (dateString: string): boolean => {
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(dateString)) return false;
  
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
};
