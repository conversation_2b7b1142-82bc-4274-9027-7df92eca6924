import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors, Spacing, BorderRadius, Shadows } from '../../constants/theme';
import { formatCurrency } from '../../utils/designSystem';

interface TransactionCardProps {
  id: string;
  amount: number;
  date: string;
  note?: string;
  createdAt: string;
  onPress?: () => void;
}

export const TransactionCard: React.FC<TransactionCardProps> = ({
  id,
  amount,
  date,
  note,
  createdAt,
  onPress
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.8}
      accessibilityRole="button"
      accessibilityLabel={`Setoran ${formatCurrency(amount)} pada ${formatDate(date)}`}
      accessibilityHint="Ketuk untuk melihat detail setoran"
    >
      <LinearGradient
        colors={[Colors.background, Colors.neutral_light]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* Left side - Icon and Amount */}
          <View style={styles.leftSection}>
            <View style={styles.iconContainer}>
              <LinearGradient
                colors={[Colors.success, '#4CAF50']}
                style={styles.iconGradient}
              >
                <Ionicons name="add" size={24} color="#fff" />
              </LinearGradient>
            </View>
            
            <View style={styles.amountSection}>
              <Text style={styles.amountText}>
                {formatCurrency(amount)}
              </Text>
              <Text style={styles.typeText}>Setoran</Text>
            </View>
          </View>

          {/* Right side - Date and Note */}
          <View style={styles.rightSection}>
            <View style={styles.dateSection}>
              <Text style={styles.dateText}>{formatDate(date)}</Text>
              <Text style={styles.timeText}>{formatTime(createdAt)}</Text>
            </View>
            
            {note && (
              <View style={styles.noteSection}>
                <Text style={styles.noteText} numberOfLines={2}>
                  {note}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Status indicator */}
        <View style={styles.statusIndicator}>
          <View style={styles.statusDot} />
          <Text style={styles.statusText}>Berhasil</Text>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
    ...Shadows.card,
  },
  
  gradient: {
    padding: Spacing.padding.card,
  },
  
  content: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.md,
    marginBottom: Spacing.sm,
  },
  
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
    flex: 1,
  },
  
  iconContainer: {
    borderRadius: BorderRadius.md,
    overflow: 'hidden',
  },
  
  iconGradient: {
    width: 48,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  amountSection: {
    flex: 1,
  },
  
  amountText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.success,
    marginBottom: 2,
  },
  
  typeText: {
    fontSize: 14,
    color: Colors.text_secondary,
  },
  
  rightSection: {
    alignItems: 'flex-end',
    gap: Spacing.xs,
  },
  
  dateSection: {
    alignItems: 'flex-end',
  },
  
  dateText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text_primary,
  },
  
  timeText: {
    fontSize: 12,
    color: Colors.text_secondary,
  },
  
  noteSection: {
    maxWidth: 120,
  },
  
  noteText: {
    fontSize: 12,
    color: Colors.text_secondary,
    fontStyle: 'italic',
    textAlign: 'right',
  },
  
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    paddingTop: Spacing.xs,
    borderTopWidth: 1,
    borderTopColor: Colors.light,
  },
  
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.success,
  },
  
  statusText: {
    fontSize: 12,
    color: Colors.success,
    fontWeight: '500',
  },
});

export default TransactionCard;
