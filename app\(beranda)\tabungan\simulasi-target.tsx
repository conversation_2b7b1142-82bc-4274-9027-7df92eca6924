import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  View,
  Text,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { FormCard } from '../../../components/tabungan/FormCard';
import { InputField } from '../../../components/tabungan/InputField';
import { CalculatorCard } from '../../../components/tabungan/CalculatorCard';
import { Colors, Spacing, BorderRadius } from '../../../constants/theme';

interface CalculationResult {
  monthsNeeded: number;
  monthlyAmount: number;
  totalTarget: number;
  currentSavings: number;
  remainingAmount: number;
}

export default function SimulasiTargetScreen() {
  const [targetAmount, setTargetAmount] = useState('');
  const [monthlyDeposit, setMonthlyDeposit] = useState('');
  const [focusedInput, setFocusedInput] = useState<string | null>(null);

  // Mock current savings - in real app, this would come from database
  const currentSavings = 2500000;

  const formatCurrency = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '');
    if (numericValue) {
      return new Intl.NumberFormat('id-ID').format(parseInt(numericValue));
    }
    return '';
  };

  const handleTargetAmountChange = (text: string) => {
    const formatted = formatCurrency(text);
    setTargetAmount(formatted);
  };

  const handleMonthlyDepositChange = (text: string) => {
    const formatted = formatCurrency(text);
    setMonthlyDeposit(formatted);
  };

  const getNumericValue = (formattedValue: string) => {
    return parseInt(formattedValue.replace(/[^0-9]/g, '')) || 0;
  };

  const calculateSimulation = (): CalculationResult | null => {
    const target = getNumericValue(targetAmount);
    const monthly = getNumericValue(monthlyDeposit);
    
    if (target <= 0 || monthly <= 0) return null;

    const remaining = target - currentSavings;
    if (remaining <= 0) {
      return {
        monthsNeeded: 0,
        monthlyAmount: monthly,
        totalTarget: target,
        currentSavings,
        remainingAmount: 0,
      };
    }

    const monthsNeeded = Math.ceil(remaining / monthly);
    
    return {
      monthsNeeded,
      monthlyAmount: monthly,
      totalTarget: target,
      currentSavings,
      remainingAmount: remaining,
    };
  };

  const simulation = calculateSimulation();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <LinearGradient
          colors={[Colors.base, Colors.medium]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.header}>
            <Text style={styles.headerTitle}>🧮 Simulasi Target</Text>
            <Text style={styles.headerSubtitle}>
              Hitung berapa lagi yang perlu ditabung
            </Text>
          </View>
        </LinearGradient>

        {/* Current Savings Display */}
        <View style={styles.currentSavingsSection}>
          <View style={styles.currentSavingsCard}>
            <LinearGradient
              colors={[Colors.success, '#4CAF50']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.currentSavingsGradient}
            >
              <Ionicons name="wallet" size={32} color="#fff" />
              <View style={styles.currentSavingsInfo}>
                <Text style={styles.currentSavingsLabel}>Tabungan Saat Ini</Text>
                <Text style={styles.currentSavingsAmount}>
                  {new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    minimumFractionDigits: 0,
                  }).format(currentSavings)}
                </Text>
              </View>
            </LinearGradient>
          </View>
        </View>

        {/* Input Form */}
        <FormCard
          title="Parameter Simulasi"
          subtitle="Masukkan target dan rencana setoran bulanan"
        >
          <InputField
            label="Target Tabungan *"
            icon="flag"
            value={targetAmount}
            onChangeText={handleTargetAmountChange}
            placeholder="Masukkan target tabungan"
            keyboardType="numeric"
            onFocus={() => setFocusedInput('target')}
            onBlur={() => setFocusedInput(null)}
            focused={focusedInput === 'target'}
          />

          <InputField
            label="Setoran per Bulan *"
            icon="calendar"
            value={monthlyDeposit}
            onChangeText={handleMonthlyDepositChange}
            placeholder="Masukkan rencana setoran bulanan"
            keyboardType="numeric"
            onFocus={() => setFocusedInput('monthly')}
            onBlur={() => setFocusedInput(null)}
            focused={focusedInput === 'monthly'}
          />
        </FormCard>

        {/* Calculation Result */}
        <CalculatorCard
          title="Hasil Simulasi"
          result={simulation}
          icon="calculator"
        />

        {/* Tips Card */}
        <FormCard
          title="Tips Menabung"
          subtitle="Saran untuk mencapai target lebih cepat"
        >
          <View style={styles.tipsContainer}>
            <View style={styles.tipItem}>
              <Ionicons name="bulb" size={20} color={Colors.warning} />
              <Text style={styles.tipText}>
                Tingkatkan setoran bulanan untuk mencapai target lebih cepat
              </Text>
            </View>
            
            <View style={styles.tipItem}>
              <Ionicons name="trending-up" size={20} color={Colors.success} />
              <Text style={styles.tipText}>
                Konsisten menabung setiap bulan lebih penting dari jumlah besar sekali
              </Text>
            </View>
            
            <View style={styles.tipItem}>
              <Ionicons name="shield-checkmark" size={20} color={Colors.base} />
              <Text style={styles.tipText}>
                Sisihkan penghasilan untuk tabungan sebelum pengeluaran lain
              </Text>
            </View>
          </View>
        </FormCard>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  
  scrollContent: {
    paddingBottom: 100, // Space for navigation bar
  },
  
  headerGradient: {
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.md,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    marginBottom: Spacing.lg,
  },
  
  header: {
    alignItems: 'center',
  },
  
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    textAlign: 'center',
  },
  
  headerSubtitle: {
    fontSize: 16,
    color: Colors.light,
    opacity: 0.9,
    textAlign: 'center',
  },
  
  currentSavingsSection: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
  },
  
  currentSavingsCard: {
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  
  currentSavingsGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
    gap: Spacing.md,
  },
  
  currentSavingsInfo: {
    flex: 1,
  },
  
  currentSavingsLabel: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    marginBottom: 4,
  },
  
  currentSavingsAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  
  tipsContainer: {
    gap: Spacing.md,
  },
  
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.sm,
    padding: Spacing.sm,
    backgroundColor: Colors.neutral_light,
    borderRadius: BorderRadius.sm,
  },
  
  tipText: {
    flex: 1,
    fontSize: 14,
    color: Colors.text_primary,
    lineHeight: 20,
  },
});
