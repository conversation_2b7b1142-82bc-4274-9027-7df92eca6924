import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React from 'react';
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { BorderRadius, Colors, Shadows, Spacing } from '../../../constants/theme';

export default function TabunganMenuScreen() {
  const menuItems = [
    {
      id: 'tambah-setoran',
      title: 'Tambah Setoran Baru',
      subtitle: 'Catat setoran tabungan Anda',
      icon: 'add-circle' as const,
      color: '#4CAF50',
      route: '/(beranda)/tabungan/tambah-setoran' as const,
    },
    {
      id: 'riwayat-setoran',
      title: 'Riwayat Setoran',
      subtitle: 'Lihat semua setoran yang sudah dibuat',
      icon: 'list' as const,
      color: '#2196F3',
      route: '/(beranda)/tabungan/riwayat-setoran' as const,
    },
    {
      id: 'simulasi-target',
      title: 'Simulasi Target',
      subtitle: 'Hitung berapa lagi yang perlu ditabung',
      icon: 'calculator' as const,
      color: '#FF9800',
      route: '/(beranda)/tabungan/simulasi-target' as const,
    },
  ];

  const handleMenuPress = (route: string) => {
    router.push(route as any);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <LinearGradient
          colors={[Colors.base, Colors.medium]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.header}>
            <Text style={styles.headerTitle}>🕌 Menu Tabungan</Text>
            <Text style={styles.headerSubtitle}>
              Kelola tabungan haji Anda dengan mudah
            </Text>
          </View>
        </LinearGradient>

        {/* Menu Items */}
        <View style={styles.menuContainer}>
          {menuItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.menuItem}
              onPress={() => handleMenuPress(item.route)}
              activeOpacity={0.8}
              accessibilityRole="button"
              accessibilityLabel={`${item.title}. ${item.subtitle}`}
              accessibilityHint="Ketuk untuk membuka menu ini"
              accessible={true}
            >
              <LinearGradient
                colors={[Colors.background, Colors.neutral_light]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.menuGradient}
              >
                <View style={[styles.menuIcon, { backgroundColor: item.color }]}>
                  <Ionicons name={item.icon} size={28} color="#fff" />
                </View>
                <View style={styles.menuContent}>
                  <Text style={styles.menuTitle}>{item.title}</Text>
                  <Text style={styles.menuSubtitle}>{item.subtitle}</Text>
                </View>
                <View style={styles.menuArrow}>
                  <Ionicons name="chevron-forward" size={20} color={Colors.text_secondary} />
                </View>
              </LinearGradient>
            </TouchableOpacity>
          ))}
        </View>

        {/* Info Card */}
        <View style={styles.infoCard}>
          <LinearGradient
            colors={[Colors.light, Colors.neutral_light]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.infoGradient}
          >
            <View style={styles.infoIcon}>
              <Ionicons name="information-circle" size={24} color={Colors.base} />
            </View>
            <Text style={styles.infoText}>
              Gunakan menu di atas untuk mengelola tabungan haji Anda.
              Semua data akan tersimpan dengan aman dan terpercaya.
            </Text>
          </LinearGradient>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  scrollContent: {
    paddingBottom: 100, // Space for navigation bar
  },

  headerGradient: {
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.md,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    marginBottom: Spacing.lg,
  },

  header: {
    alignItems: 'center',
  },

  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    textAlign: 'center',
  },

  headerSubtitle: {
    fontSize: 16,
    color: Colors.light,
    opacity: 0.9,
    textAlign: 'center',
  },

  menuContainer: {
    paddingHorizontal: Spacing.md,
    gap: Spacing.md,
    marginBottom: Spacing.lg,
  },

  menuItem: {
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
    ...Shadows.card,
  },

  menuGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.padding.card,
    gap: Spacing.md,
    minHeight: 80, // Accessibility touch target
  },

  menuIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },

  menuContent: {
    flex: 1,
    gap: 4,
  },

  menuTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text_primary,
  },

  menuSubtitle: {
    fontSize: 14,
    color: Colors.text_secondary,
    lineHeight: 20,
  },

  menuArrow: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },

  infoCard: {
    marginHorizontal: Spacing.md,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
    ...Shadows.card,
  },

  infoGradient: {
    flexDirection: 'row',
    padding: Spacing.padding.card,
    gap: Spacing.md,
    alignItems: 'flex-start',
  },

  infoIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2,
  },

  infoText: {
    flex: 1,
    fontSize: 14,
    color: Colors.text_primary,
    lineHeight: 20,
  },
});
