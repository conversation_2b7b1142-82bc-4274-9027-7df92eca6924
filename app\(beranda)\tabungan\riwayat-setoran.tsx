import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  RefreshControl,
  StyleSheet,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { TransactionCard } from '../../../components/tabungan/TransactionCard';
import { ActionButton } from '../../../components/tabungan/ActionButton';
import { Colors, Spacing, BorderRadius } from '../../../constants/theme';
import { formatCurrency } from '../../../utils/designSystem';

interface Setoran {
  id: string;
  amount: number;
  date: string;
  note?: string;
  createdAt: string;
}

export default function RiwayatSetoranScreen() {
  const [setoranList, setSetoranList] = useState<Setoran[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Mock data - in real app, this would come from database
  const mockData: Setoran[] = [
    {
      id: '1',
      amount: 500000,
      date: '2024-01-15',
      note: 'Setoran rutin bulanan',
      createdAt: '2024-01-15T10:30:00Z',
    },
    {
      id: '2',
      amount: 250000,
      date: '2024-01-08',
      note: 'Bonus dari pekerjaan',
      createdAt: '2024-01-08T14:20:00Z',
    },
    {
      id: '3',
      amount: 100000,
      date: '2024-01-01',
      note: 'Setoran awal tahun',
      createdAt: '2024-01-01T09:00:00Z',
    },
    {
      id: '4',
      amount: 300000,
      date: '2023-12-25',
      note: 'Setoran akhir tahun',
      createdAt: '2023-12-25T16:45:00Z',
    },
    {
      id: '5',
      amount: 150000,
      date: '2023-12-15',
      createdAt: '2023-12-15T11:20:00Z',
    },
  ];

  useEffect(() => {
    loadSetoranData();
  }, []);

  const loadSetoranData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSetoranList(mockData);
    } catch (error) {
      console.error('Error loading setoran data:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadSetoranData();
    setRefreshing(false);
  };

  const handleSetoranPress = (setoran: Setoran) => {
    // Navigate to detail view or show modal
    console.log('Setoran pressed:', setoran);
  };

  const handleAddSetoran = () => {
    router.push('/(beranda)/tabungan/tambah-setoran');
  };

  const getTotalAmount = () => {
    return setoranList.reduce((total, setoran) => total + setoran.amount, 0);
  };

  const getThisMonthAmount = () => {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    return setoranList
      .filter(setoran => {
        const setoranDate = new Date(setoran.date);
        return setoranDate.getMonth() === currentMonth && 
               setoranDate.getFullYear() === currentYear;
      })
      .reduce((total, setoran) => total + setoran.amount, 0);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <LinearGradient
          colors={[Colors.base, Colors.medium]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.header}>
            <Text style={styles.headerTitle}>📊 Riwayat Setoran</Text>
            <Text style={styles.headerSubtitle}>
              Lihat semua setoran yang sudah dibuat
            </Text>
          </View>
        </LinearGradient>

        {/* Summary Cards */}
        <View style={styles.summarySection}>
          <View style={styles.summaryGrid}>
            <View style={styles.summaryCard}>
              <LinearGradient
                colors={[Colors.success, '#4CAF50']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.summaryGradient}
              >
                <Ionicons name="wallet" size={24} color="#fff" />
                <Text style={styles.summaryValue}>
                  {formatCurrency(getTotalAmount())}
                </Text>
                <Text style={styles.summaryLabel}>Total Setoran</Text>
              </LinearGradient>
            </View>

            <View style={styles.summaryCard}>
              <LinearGradient
                colors={[Colors.base, Colors.medium]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.summaryGradient}
              >
                <Ionicons name="calendar" size={24} color="#fff" />
                <Text style={styles.summaryValue}>
                  {formatCurrency(getThisMonthAmount())}
                </Text>
                <Text style={styles.summaryLabel}>Bulan Ini</Text>
              </LinearGradient>
            </View>
          </View>
        </View>

        {/* Action Button */}
        <View style={styles.actionContainer}>
          <ActionButton
            title="Tambah Setoran Baru"
            onPress={handleAddSetoran}
            icon="add-circle"
            variant="primary"
          />
        </View>

        {/* Transaction List */}
        <View style={styles.listSection}>
          <View style={styles.listHeader}>
            <Text style={styles.listTitle}>Riwayat Transaksi</Text>
            <Text style={styles.listSubtitle}>
              {setoranList.length} setoran tercatat
            </Text>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Memuat data...</Text>
            </View>
          ) : setoranList.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="document-text" size={48} color={Colors.text_secondary} />
              <Text style={styles.emptyTitle}>Belum Ada Setoran</Text>
              <Text style={styles.emptySubtitle}>
                Mulai menabung dengan menambah setoran pertama Anda
              </Text>
            </View>
          ) : (
            <View style={styles.transactionList}>
              {setoranList.map((setoran) => (
                <TransactionCard
                  key={setoran.id}
                  {...setoran}
                  onPress={() => handleSetoranPress(setoran)}
                />
              ))}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  
  scrollContent: {
    paddingBottom: 100, // Space for navigation bar
  },
  
  headerGradient: {
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.md,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    marginBottom: Spacing.lg,
  },
  
  header: {
    alignItems: 'center',
  },
  
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    textAlign: 'center',
  },
  
  headerSubtitle: {
    fontSize: 16,
    color: Colors.light,
    opacity: 0.9,
    textAlign: 'center',
  },
  
  summarySection: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
  },
  
  summaryGrid: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  
  summaryCard: {
    flex: 1,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  
  summaryGradient: {
    padding: Spacing.lg,
    alignItems: 'center',
    gap: Spacing.sm,
  },
  
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
  },
  
  summaryLabel: {
    fontSize: 12,
    color: '#fff',
    opacity: 0.9,
    textAlign: 'center',
  },
  
  actionContainer: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
  },
  
  listSection: {
    flex: 1,
  },
  
  listHeader: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.md,
  },
  
  listTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text_primary,
    marginBottom: 4,
  },
  
  listSubtitle: {
    fontSize: 14,
    color: Colors.text_secondary,
  },
  
  loadingContainer: {
    padding: Spacing.xl,
    alignItems: 'center',
  },
  
  loadingText: {
    fontSize: 16,
    color: Colors.text_secondary,
  },
  
  emptyContainer: {
    padding: Spacing.xl,
    alignItems: 'center',
    gap: Spacing.md,
  },
  
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text_primary,
  },
  
  emptySubtitle: {
    fontSize: 14,
    color: Colors.text_secondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  
  transactionList: {
    gap: 0, // TransactionCard has its own margin
  },
});
