import { Ionicons } from '@expo/vector-icons';
import { Tabs } from 'expo-router';
import React from 'react';
import { BorderRadius, Colors, Shadows } from '../../constants/theme';

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          height: 80,
          paddingBottom: 10,
          paddingTop: 10,
          backgroundColor: Colors.background,
          borderTopLeftRadius: BorderRadius.xl,
          borderTopRightRadius: BorderRadius.xl,
          borderTopWidth: 0,
          position: 'absolute',
          ...Shadows.floating,
        },
        tabBarLabelStyle: {
          fontSize: 16,
          fontWeight: '600',
          color: Colors.text_primary,
        },
        tabBarIconStyle: {
          marginBottom: 5,
        },
        tabBarActiveTintColor: Colors.base,
        tabBarInactiveTintColor: Colors.text_secondary,
        tabBarItemStyle: {
          borderRadius: BorderRadius.md,
          marginHorizontal: 8,
          paddingVertical: 4,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Beranda',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? "home" : "home-outline"}
              size={size + 4}
              color={color}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="tabungan"
        options={{
          title: 'Tabungan',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? "wallet" : "wallet-outline"}
              size={size + 4}
              color={color}
            />
          ),
        }}
      />
    </Tabs>
  );
}
