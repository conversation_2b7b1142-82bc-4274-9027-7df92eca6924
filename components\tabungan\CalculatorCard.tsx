import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors, Spacing, BorderRadius, Shadows } from '../../constants/theme';
import { formatCurrency } from '../../utils/designSystem';

interface CalculationResult {
  monthsNeeded: number;
  monthlyAmount: number;
  totalTarget: number;
  currentSavings: number;
  remainingAmount: number;
}

interface CalculatorCardProps {
  title: string;
  result: CalculationResult | null;
  icon?: keyof typeof Ionicons.glyphMap;
}

export const CalculatorCard: React.FC<CalculatorCardProps> = ({
  title,
  result,
  icon = 'calculator'
}) => {
  if (!result) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={[Colors.neutral_light, Colors.background]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradient}
        >
          <View style={styles.emptyState}>
            <Ionicons name={icon} size={48} color={Colors.text_secondary} />
            <Text style={styles.emptyText}>
              Masukkan data untuk melihat hasil simulasi
            </Text>
          </View>
        </LinearGradient>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[Colors.light, Colors.neutral_light]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <LinearGradient
              colors={[Colors.base, Colors.medium]}
              style={styles.iconGradient}
            >
              <Ionicons name={icon} size={24} color="#fff" />
            </LinearGradient>
          </View>
          <Text style={styles.title}>{title}</Text>
        </View>

        <View style={styles.content}>
          {/* Main Result */}
          <View style={styles.mainResult}>
            <Text style={styles.resultLabel}>Waktu yang Dibutuhkan</Text>
            <Text style={styles.resultValue}>
              {result.monthsNeeded} Bulan
            </Text>
          </View>

          {/* Details Grid */}
          <View style={styles.detailsGrid}>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Setoran per Bulan</Text>
              <Text style={styles.detailValue}>
                {formatCurrency(result.monthlyAmount)}
              </Text>
            </View>

            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Target Total</Text>
              <Text style={styles.detailValue}>
                {formatCurrency(result.totalTarget)}
              </Text>
            </View>

            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Tabungan Saat Ini</Text>
              <Text style={styles.detailValue}>
                {formatCurrency(result.currentSavings)}
              </Text>
            </View>

            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Sisa yang Dibutuhkan</Text>
              <Text style={[styles.detailValue, styles.remainingValue]}>
                {formatCurrency(result.remainingAmount)}
              </Text>
            </View>
          </View>

          {/* Progress Indicator */}
          <View style={styles.progressSection}>
            <Text style={styles.progressLabel}>Progress Saat Ini</Text>
            <View style={styles.progressBar}>
              <LinearGradient
                colors={[Colors.base, Colors.medium]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={[
                  styles.progressFill,
                  { 
                    width: `${Math.min((result.currentSavings / result.totalTarget) * 100, 100)}%` 
                  }
                ]}
              />
            </View>
            <Text style={styles.progressText}>
              {Math.round((result.currentSavings / result.totalTarget) * 100)}% tercapai
            </Text>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
    ...Shadows.card,
  },
  
  gradient: {
    padding: Spacing.padding.card,
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
    marginBottom: Spacing.lg,
  },
  
  iconContainer: {
    borderRadius: BorderRadius.md,
    overflow: 'hidden',
  },
  
  iconGradient: {
    width: 48,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text_primary,
    flex: 1,
  },
  
  content: {
    gap: Spacing.lg,
  },
  
  mainResult: {
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
  },
  
  resultLabel: {
    fontSize: 14,
    color: Colors.text_secondary,
    marginBottom: Spacing.xs,
  },
  
  resultValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.base,
  },
  
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },
  
  detailItem: {
    flex: 1,
    minWidth: '45%',
    padding: Spacing.md,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.sm,
  },
  
  detailLabel: {
    fontSize: 12,
    color: Colors.text_secondary,
    marginBottom: Spacing.xs,
  },
  
  detailValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text_primary,
  },
  
  remainingValue: {
    color: Colors.medium,
  },
  
  progressSection: {
    gap: Spacing.sm,
  },
  
  progressLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text_primary,
  },
  
  progressBar: {
    height: 8,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.pill,
    overflow: 'hidden',
  },
  
  progressFill: {
    height: '100%',
    borderRadius: BorderRadius.pill,
  },
  
  progressText: {
    fontSize: 12,
    color: Colors.text_secondary,
    textAlign: 'center',
  },
  
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.xl,
    gap: Spacing.md,
  },
  
  emptyText: {
    fontSize: 16,
    color: Colors.text_secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default CalculatorCard;
