import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors, Spacing, BorderRadius, Shadows } from '../../constants/theme';

interface FormCardProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  style?: ViewStyle;
}

export const FormCard: React.FC<FormCardProps> = ({
  title,
  subtitle,
  children,
  style
}) => {
  return (
    <View style={[styles.container, style]}>
      <LinearGradient
        colors={[Colors.background, Colors.neutral_light]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradient}
      >
        <View style={styles.header}>
          <Text style={styles.title}>{title}</Text>
          {subtitle && (
            <Text style={styles.subtitle}>{subtitle}</Text>
          )}
        </View>
        
        <View style={styles.content}>
          {children}
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
    ...Shadows.card,
  },
  
  gradient: {
    padding: Spacing.padding.card,
  },
  
  header: {
    marginBottom: Spacing.md,
    paddingBottom: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light,
  },
  
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text_primary,
    marginBottom: 4,
  },
  
  subtitle: {
    fontSize: 14,
    color: Colors.text_secondary,
    lineHeight: 20,
  },
  
  content: {
    gap: Spacing.md,
  },
});

export default FormCard;
