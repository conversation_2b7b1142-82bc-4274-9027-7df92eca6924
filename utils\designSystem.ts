// Design System Utilities for SaveMoney App
import { BorderRadius, Colors, Shadows, Spacing, Typography } from '../constants/theme';

// Color utilities
export const getColor = (colorKey: string): string => {
  return (Colors as any)[colorKey] || Colors.base;
};

export const getGradient = (gradientKey: keyof typeof Colors.gradients): string[] => {
  return Colors.gradients[gradientKey] || Colors.gradients.primary;
};

// Typography utilities
export const getTypographyStyle = (variant: 'h1' | 'h2' | 'body' | 'caption' | 'small') => {
  return {
    fontSize: Typography.sizes[variant],
    fontFamily: Typography.fonts.primary,
    color: Colors.text_primary,
    lineHeight: Typography.lineHeights.normal * Typography.sizes[variant],
  };
};

export const getTypographyWithWeight = (
  variant: 'h1' | 'h2' | 'body' | 'caption' | 'small',
  weight: 'regular' | 'medium' | 'semibold' | 'bold' = 'regular'
) => {
  let fontFamily = Typography.fonts.primary;

  switch (weight) {
    case 'medium':
      fontFamily = Typography.fonts.primaryMedium;
      break;
    case 'semibold':
      fontFamily = Typography.fonts.primarySemiBold;
      break;
    case 'bold':
      fontFamily = Typography.fonts.primaryBold;
      break;
    default:
      fontFamily = Typography.fonts.primary;
  }

  return {
    fontSize: Typography.sizes[variant],
    fontFamily,
    color: Colors.text_primary,
    lineHeight: Typography.lineHeights.normal * Typography.sizes[variant],
  };
};

// Spacing utilities
export const getSpacing = (multiplier: number): number => {
  return Spacing.grid * multiplier;
};

export const getMargin = (direction?: 'top' | 'bottom' | 'left' | 'right' | 'horizontal' | 'vertical') => {
  const baseMargin = Spacing.margins;
  
  switch (direction) {
    case 'top':
      return { marginTop: baseMargin };
    case 'bottom':
      return { marginBottom: baseMargin };
    case 'left':
      return { marginLeft: baseMargin };
    case 'right':
      return { marginRight: baseMargin };
    case 'horizontal':
      return { marginHorizontal: baseMargin };
    case 'vertical':
      return { marginVertical: baseMargin };
    default:
      return { margin: baseMargin };
  }
};

export const getPadding = (multiplier: number = 1, direction?: 'top' | 'bottom' | 'left' | 'right' | 'horizontal' | 'vertical') => {
  const basePadding = Spacing.md * multiplier;
  
  switch (direction) {
    case 'top':
      return { paddingTop: basePadding };
    case 'bottom':
      return { paddingBottom: basePadding };
    case 'left':
      return { paddingLeft: basePadding };
    case 'right':
      return { paddingRight: basePadding };
    case 'horizontal':
      return { paddingHorizontal: basePadding };
    case 'vertical':
      return { paddingVertical: basePadding };
    default:
      return { padding: basePadding };
  }
};

// Border radius utilities
export const getBorderRadius = (size: keyof typeof BorderRadius): number => {
  return BorderRadius[size];
};

export const getRoundedStyle = (size: keyof typeof BorderRadius = 'md') => {
  return {
    borderRadius: BorderRadius[size],
  };
};

// Shadow utilities
export const getShadow = (type: keyof typeof Shadows) => {
  return Shadows[type];
};

// Card utilities
export const getCardStyle = (variant: 'default' | 'elevated' | 'outlined' = 'default') => {
  const baseStyle = {
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.card,
    padding: Spacing.padding.card,
  };

  switch (variant) {
    case 'elevated':
      return {
        ...baseStyle,
        ...Shadows.card,
      };
    case 'outlined':
      return {
        ...baseStyle,
        borderWidth: 1,
        borderColor: Colors.light,
      };
    default:
      return baseStyle;
  }
};

// Button utilities
export const getButtonStyle = (variant: 'primary' | 'secondary' | 'outline' = 'primary') => {
  const baseStyle = {
    height: 48,
    paddingHorizontal: 24,
    borderRadius: BorderRadius.button,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    minWidth: 48, // Accessibility
  };

  switch (variant) {
    case 'primary':
      return {
        ...baseStyle,
        backgroundColor: Colors.base,
      };
    case 'secondary':
      return {
        ...baseStyle,
        backgroundColor: Colors.light,
      };
    case 'outline':
      return {
        ...baseStyle,
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: Colors.base,
      };
    default:
      return baseStyle;
  }
};

// Progress utilities
export const getProgressStyle = (percentage: number) => {
  return {
    width: `${Math.min(Math.max(percentage, 0), 100)}%`,
    height: 8,
    backgroundColor: Colors.base,
    borderRadius: BorderRadius.pill,
  };
};

// Accessibility utilities
export const getAccessibleTouchTarget = () => {
  return {
    minHeight: 48,
    minWidth: 48,
  };
};

export const getHighContrastStyle = () => {
  return {
    color: Colors.text_primary,
    backgroundColor: Colors.background,
  };
};

// Animation utilities
export const getFadeInAnimation = (duration: number = 300) => {
  return {
    opacity: 1,
    transform: [{ scale: 1 }],
    transition: `all ${duration}ms ease-in-out`,
  };
};

// Currency formatting utility
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
  }).format(amount);
};

// Responsive utilities
export const getResponsiveStyle = (screenWidth: number) => {
  if (screenWidth < 375) {
    // Small screens
    return {
      padding: Spacing.sm,
      fontSize: Typography.sizes.caption,
    };
  } else if (screenWidth > 414) {
    // Large screens
    return {
      padding: Spacing.lg,
      fontSize: Typography.sizes.body,
    };
  }
  
  // Default medium screens
  return {
    padding: Spacing.md,
    fontSize: Typography.sizes.body,
  };
};

export default {
  getColor,
  getGradient,
  getTypographyStyle,
  getTypographyWithWeight,
  getSpacing,
  getMargin,
  getPadding,
  getBorderRadius,
  getRoundedStyle,
  getShadow,
  getCardStyle,
  getButtonStyle,
  getProgressStyle,
  getAccessibleTouchTarget,
  getHighContrastStyle,
  getFadeInAnimation,
  formatCurrency,
  getResponsiveStyle,
};
