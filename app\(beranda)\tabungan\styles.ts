import { StyleSheet } from 'react-native';

export const tabunganStyles = StyleSheet.create({
  // Main container
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 20,
  },

  // Header section
  header: {
    backgroundColor: '#fff',
    padding: 24,
    borderRadius: 15,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2E7D32',
    textAlign: 'center',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },

  // Menu container
  menuContainer: {
    gap: 16,
    marginBottom: 24,
  },

  // Menu item - Enhanced for elderly users
  menuItem: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 24, // Increased padding
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    minHeight: 100, // Minimum height for easier tapping
  },
  menuIcon: {
    width: 70, // Larger icon container
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 20, // More spacing
  },
  menuContent: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 20, // Larger font for better readability
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 6, // More spacing
    lineHeight: 26, // Better line height
  },
  menuSubtitle: {
    fontSize: 16, // Larger subtitle font
    color: '#666',
    lineHeight: 22, // Better line height
  },
  menuArrow: {
    marginLeft: 8,
  },

  // Info card
  infoCard: {
    backgroundColor: '#E3F2FD',
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    alignItems: 'flex-start',
  },
  infoIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#1976D2',
    lineHeight: 20,
  },

  // Form styles - Enhanced for elderly users
  formContainer: {
    backgroundColor: '#fff',
    padding: 28, // More padding
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  formTitle: {
    fontSize: 24, // Larger title
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 24, // More spacing
    textAlign: 'center',
    lineHeight: 30,
  },
  inputContainer: {
    marginBottom: 24, // More spacing between inputs
  },
  label: {
    fontSize: 18, // Larger label font
    fontWeight: '600',
    color: '#333',
    marginBottom: 10, // More spacing
    lineHeight: 24,
  },
  input: {
    borderWidth: 2,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    padding: 20, // More padding for easier interaction
    fontSize: 20, // Larger input text
    backgroundColor: '#f9f9f9',
    color: '#333',
    minHeight: 56, // Minimum height for easier tapping
    lineHeight: 24,
  },
  inputFocused: {
    borderColor: '#2E7D32',
    backgroundColor: '#fff',
    borderWidth: 3, // Thicker border when focused
  },
  button: {
    backgroundColor: '#2E7D32',
    borderRadius: 12,
    padding: 20, // More padding
    alignItems: 'center',
    marginTop: 16,
    minHeight: 60, // Minimum height for easier tapping
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: '#fff',
    fontSize: 20, // Larger button text
    fontWeight: 'bold',
    lineHeight: 24,
  },

  // List styles - Enhanced for elderly users
  listContainer: {
    gap: 16, // More spacing between items
  },
  listItem: {
    backgroundColor: '#fff',
    padding: 24, // More padding
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
    minHeight: 80, // Minimum height for better visibility
  },
  listItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10, // More spacing
  },
  listItemDate: {
    fontSize: 16, // Larger date font
    color: '#666',
    lineHeight: 22,
    flex: 1, // Allow wrapping if needed
  },
  listItemAmount: {
    fontSize: 20, // Larger amount font
    fontWeight: 'bold',
    color: '#2E7D32',
    lineHeight: 26,
    textAlign: 'right',
  },
  listItemNote: {
    fontSize: 16, // Larger note font
    color: '#666',
    fontStyle: 'italic',
    lineHeight: 22,
    marginTop: 4,
  },

  // Empty state - Enhanced for elderly users
  emptyState: {
    alignItems: 'center',
    padding: 48, // More padding
  },
  emptyStateIcon: {
    marginBottom: 20, // More spacing
  },
  emptyStateText: {
    fontSize: 18, // Larger font
    color: '#666',
    textAlign: 'center',
    lineHeight: 26, // Better line height
    maxWidth: 300, // Limit width for better readability
  },

  // Currency display - Enhanced for elderly users
  currencyContainer: {
    backgroundColor: '#E8F5E8',
    padding: 28, // More padding
    borderRadius: 15, // Slightly larger radius
    marginBottom: 20,
    alignItems: 'center',
    minHeight: 120, // Minimum height for better visibility
  },
  currencyLabel: {
    fontSize: 18, // Larger label
    color: '#2E7D32',
    marginBottom: 12, // More spacing
    fontWeight: '600',
    lineHeight: 24,
  },
  currencyAmount: {
    fontSize: 36, // Even larger amount
    fontWeight: 'bold',
    color: '#2E7D32',
    lineHeight: 42,
    textAlign: 'center',
  },
});
